//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router, usePage } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import {
    FiGlobe,
    FiUsers,
    <PERSON><PERSON>ist,
    <PERSON><PERSON>ser,
    FiFileText,
    Fi<PERSON>lock,
} from "react-icons/fi";
import {
    MdOutlinePersonOutline,
    MdExitToApp,
    MdSecurity,
    MdOutlineCheckCircle,
    MdOutlineLibraryAddCheck,
    MdOutlineContactPage,
    MdOutlineSystemUpdateAlt,
    MdOutlineVerifiedUser,
} from "react-icons/md";
import { HiOutlinePencilSquare } from "react-icons/hi2";
import { TbCategoryPlus } from "react-icons/tb";

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialDashboardActivityLogs(
    {
        //! PROPS
        //... 
        
        //! STATES 
        //...
        
        //! EVENTS
        //...
    }
)
{
    //! PACKAGE
    //... 

    //! VARIABLES
    //...

    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const {
        logs,
    } = usePage().props;    

    const typeIcons =
    {
        SIGN_IN              : <MdOutlineCheckCircle />,
        SIGN_OUT             : <MdExitToApp />,
        REGISTER             : <MdOutlineLibraryAddCheck />,
        PROFILE_UPDATE       : <HiOutlinePencilSquare />,
        SECURITY_UPDATE      : <MdSecurity />,
        CATEGORY_UPDATE      : <TbCategoryPlus />,
        CONTACT_UPDATE       : <MdOutlineContactPage />,
        DOMAIN_UPDATE        : <MdOutlineSystemUpdateAlt />,
        IDENTITY_VERIFICATION: <MdOutlineVerifiedUser />,
    };

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function getStatusLabel(type) 
    {
        const normalizedType = type.replace(/ /g, "_").toUpperCase();
        const label          = (logs[0]?.statuses && logs[0].statuses[normalizedType]) || type;
        const icon           = typeIcons[normalizedType] || <MdOutlinePersonOutline />;
        
        return { label, icon };
    };

    return (
        <div className="bg-gray-100 p-4 rounded-xl w-full">
            <div className="flex items-center gap-2 mb-4">
                <FiClock className="text-xl" />
                <h2 className="text-lg font-semibold">
                    Client Activity Logs
                </h2>
            </div>
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                {logs.map((log) =>
                    (log.logs || []).map((lg) => {
                        const { label, icon } = getStatusLabel(
                            lg.type
                        );
                        {JSON.stringify(lg.statuses)}
                        return (
                            <div key={lg.id}>
                                <div className="text-sm font-semibold text-gray-800 mb-2 flex items-center">
                                    <div className="bg-white rounded-md border border-gray-300 px-4 py-2 font-bold">
                                        {lg.formatted_created_at}
                                    </div>
                                    <div className="flex-grow border-t border-gray-300 ml-2"></div>
                                </div>
                                <div className="flex items-center justify-between py-2 px-4 text-sm text-gray-700">
                                    <div className="flex items-center gap-2 overflow-x-auto">
                                        <div>{icon}</div>
                                        <div className="flex gap-1">
                                            <span className="font-medium">
                                                {label}
                                            </span>
                                            <span className="text-gray-600">
                                                - {lg.message} {lg.email}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        {new Date(
                                            lg.created_at
                                        ).toLocaleTimeString(
                                            "en-US",
                                            {
                                                hour: "2-digit",
                                                minute: "2-digit",
                                                hour12: true,
                                            }
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })
                )}
            </div>

            {
                hasPermission('client.logs.security.all')  
                    ?
                        <div className="mt-6 flex justify-center">
                            <Link
                                href={route("client.logs.security.all")}
                                className="bg-[#0077a3] text-white px-5 py-2 rounded-md font-medium hover:bg-[#005f85] transition text-xs-custom"
                            >
                                View My Activity Logs
                            </Link>
                        </div>
                    :
                        null
            }
        </div>
    );
}
