<?php

namespace App\Modules\PendingDelete\Requests;

use App\Modules\PendingDelete\Services\DomainRestoreService;
use Illuminate\Foundation\Http\FormRequest;

class DomainRestoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'ids' => ['required', 'array']
        ];
    }

    public function restore(): void
    {
        DomainRestoreService::instance()->restore($this->ids);
    }
}
