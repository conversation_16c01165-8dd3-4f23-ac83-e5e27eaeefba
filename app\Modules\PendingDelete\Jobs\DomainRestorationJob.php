<?php

namespace App\Modules\PendingDelete\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Notification\Services\NotificationService;
use App\Modules\PendingDelete\Services\DomainRestoreJobService;
use App\Modules\PendingDelete\Services\DatabaseQueryService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class DomainRestorationJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $deleteId,
        string $domainId,
        string $domainName,
        string $registeredDomainId,
        string $createdAt,
        string $updatedAt,
        string $userId,
        string $email
    ) {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'deleteId' => $deleteId,
            'domainId' => $domainId,
            'domainName' => $domainName,
            'registeredDomainId' => $registeredDomainId,
            'createdAt' => $createdAt,
            'updatedAt' => $updatedAt,
            'userId' => $userId,
            'email' => $email
        ];

        $this->onConnection(QueueConnection::DOMAIN_RESTORATION);
        $this->onQueue(QueueTypes::DOMAIN_RESTORATION[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return $this->params['deleteId'];
    }

    public function handle(): void
    {
        try {
            DomainRestoreJobService::instance()->restore($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            app(AuthLogger::class)->info('number of attempts: ' . $this->attempts());
            throw $e;
        }
    }

    public function backoff(): array
    {
        return [5, 10];
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error($exception->getMessage());

        DatabaseQueryService::instance()->insertPendingDomain($this->params);
    }
}
