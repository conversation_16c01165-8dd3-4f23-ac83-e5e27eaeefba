<?php

namespace App\Modules\RequestDelete\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\RequestDelete\Requests\ShowListRequest;
use Inertia\Inertia;

class DeleteRequestController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('RequestDelete/Index', $request->show());
    }

    public function store(ShowListRequest $request){
        $request->feedbackSave();
    }

    public function approve_delete(ShowListRequest $request){
        $request->approve_deleteRequest();
    }

    public function reject_delete(ShowListRequest $request){
        $request->reject_deleteRequest();
    }
}