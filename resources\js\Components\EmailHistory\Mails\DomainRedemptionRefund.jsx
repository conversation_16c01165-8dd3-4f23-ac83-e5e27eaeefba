import React from 'react';

function DomainRedemptionPeriod({ emailData, links }) {
    const data = JSON.parse(emailData);

    return (
        <div className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'" }}>
            <div className="p-9 bg-white max-w-xl w-full">
                <p className="font-bold mb-2">{data.greeting}</p>
                <p className="mt-4">{data.body}</p>
                <p className="mt-2">{data.body2}</p>
                <p className="mt-4"><a href={data.redirect_url}>Click here </a></p>
                <p className="mt-4 font-bold mb-2">Sincerely,</p>
                <p className="font-bold">{data.sender}</p>
            </div>
        </div>
    );
}

export default DomainRedemptionPeriod;