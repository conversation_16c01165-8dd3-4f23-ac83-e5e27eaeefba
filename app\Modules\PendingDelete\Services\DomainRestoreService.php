<?php

namespace App\Modules\PendingDelete\Services;

use App\Modules\PendingDelete\Jobs\DomainRestorationJob;
use Illuminate\Database\Query\Builder;
use App\Modules\AdminHistory\Constants\HistoryType;
use App\Events\AdminActionEvent;
use Carbon\Carbon;
use stdClass;

class DomainRestoreService
{
    private Carbon $now;
    private $isJob = false;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        $domainRestoreService = new self;

        return $domainRestoreService;
    }

    public function restore(array $ids): void
    {
        $pendingDomains = DatabaseQueryService::instance()->pendingDomains($ids);

        $this->processRestoreDispatch($pendingDomains, $ids);
        $pendingDomains->whereIn('pending_domain_deletions.id', $ids)->delete();
    }

    // PRIVATE Function

    private function jobDispatch(stdClass $domain): void
    {
        DomainRestorationJob::dispatch(
            $domain->id,
            $domain->domain_id,
            $domain->name,
            $domain->registered_domain_id,
            $domain->created_at,
            $domain->updated_at,
            $domain->user_id,
            $domain->user_email,
        );
    }

    private function whenJobIsDisabled(stdClass $domain): void
    {
        DomainRestoreJobService::instance()->restore([
            'deleteId' => $domain->id,
            'domainId' => $domain->domain_id,
            'domainName' => $domain->name,
            'registeredDomainId' => $domain->registered_domain_id,
            'createdAt' => $domain->created_at,
            'updatedAt' => $domain->updated_at,
            'userId' => $domain->user_id,
            'email' => $domain->user_email
        ]);
    }

    private function processRestoreDispatch(Builder $pendingDomains): void
    {
        $domains = DatabaseQueryService::instance()->getDomainRecords($pendingDomains);
        $domainNames = collect($domains)->pluck('name')->all();

        event(new AdminActionEvent( auth()->user()->id, HistoryType::DOMAIN_RESTORED, 'Domains have been restored: ' . implode(', ', $domainNames) . ' by ' . auth()->user()->email ));

        foreach ($domains as $domain) {
            if ($this->isJob) {
                $this->jobDispatch($domain);
                continue;
            }

            $this->whenJobIsDisabled($domain);
        }
    }
}
