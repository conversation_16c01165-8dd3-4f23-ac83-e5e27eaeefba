import Checkbox from "@/Components/Checkbox";
import Item from "@/Components/RequestDelete/Item";
import SecondaryButton from "@/Components/SecondaryButton";
import AdminLayout from "@/Layouts/AdminLayout";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useState } from "react";
import { router } from "@inertiajs/react";
import {
    MdOutlineFilterAlt,
    MdOutlineSettings,
    MdOutlineSwapVert,
} from "react-icons/md";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import "react-toastify/dist/ReactToastify.css";
import LoaderSpinner from "@/Components/LoaderSpinner";
import SearchSearchNoDomainFound from "@/Components/Domain/Search/SearchNoDomainFound";
import { _RequestDelete } from "@/Constant/_RequestDelete";
import Filter from "@/Components/RequestDelete/Filter";

export default function Index({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    const defaultStatus = () => {
        const { statusType } = route().params;
        return statusType === undefined
            ? _RequestDelete.statusTypes.pending
            : statusType;
    };
    const callSortName = () => {
        const { statusType, orderby } = route().params;

        let str = "";
        if (orderby == "domain:asc") {
            str = "domain:desc";
        } else {
            str = "domain:asc";
        }

        let payload = {};
        payload.statusType = statusType;
        payload.orderby = str;

        router.get(route("domain.delete-request.view"), payload);
    };
    const [status, setStatus] = useState(defaultStatus());
    const [selectAll, setSelectAll] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const deleteDisabled = status === _RequestDelete.statusTypes.rejected;

    const handleStatusChange = (statusType) => {
        const { orderby } = route().params;
        let payload = {};

        setSpinner(true);

        setStatus(statusType);
        if (statusType === "PENDING") {
            payload = {};
        } else {
            payload = {
                statusType: statusType,
            };
        }

        payload.orderby = orderby;
        router.get(route("domain.delete-request.view"), payload);
    };

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);

        const delete_ids = items.map((item) => item.dcrID);

        setSelectedItems(checked ? delete_ids : []);
    };

    const handleItemCheckboxChange = (itemId, checked) => {
        setSelectedItems((prevSelectedItems) => {
            return checked
                ? [...prevSelectedItems, itemId]
                : prevSelectedItems.filter((id) => id !== itemId);
        });

        let temp = [...selectedItems];

        if (temp.includes(itemId)) {
            temp = temp.filter((e) => e != itemId);
        } else {
            temp.push(itemId);
        }

        temp.length == items.length ? setSelectAll(true) : setSelectAll(false);
    };

    const [hasSpinner, setSpinner] = useState(false);

    const query = route().params;

    const [limit, setLimit] = useState(parseInt(query.limit) || 20);

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    const handleLimitChange = (e) => {
        const newLimit = parseInt(getEventValue(e));
        setLimit(newLimit);
        router.get(
            route("domain.delete-request.view"),
            { ...route().params, limit: newLimit, page: 1 },
            {
                preserveScroll: true,
                preserveState: true,
            }
        );
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1600px] mt-20 flex flex-col space-y-4">
                <div>
                    <h1 className="text-4xl font-bold pl-3">Request Delete</h1>
                    <p className="text-gray-600 pl-3">
                        List of domains that have been requested for deletion.
                    </p>
                </div>

                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px", left: "15px" }}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div
                    id="sample"
                    className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                >
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">
                            Filter:
                        </span>
                    </label>

                    <Filter status={status} />
                </div>
                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    <div
                        onClick={() =>
                            handleStatusChange(
                                _RequestDelete.statusTypes.pending
                            )
                        }
                        className={`px-5 py-1 rounded-sm ${
                            status == _RequestDelete.statusTypes.pending
                                ? "bg-gray-100 text-gray-700"
                                : "hover:bg-gray-100 hover:text-link"
                        }`}
                    >
                        <span className="text-inherit">Pending</span>
                    </div>
                    <div
                        onClick={() =>
                            handleStatusChange(
                                _RequestDelete.statusTypes.approved
                            )
                        }
                        className={`px-5 py-1 rounded-sm ${
                            status == _RequestDelete.statusTypes.approved
                                ? "bg-gray-100 text-gray-700"
                                : "hover:bg-gray-100 hover:text-link"
                        }`}
                    >
                        <span className="text-inherit">Approve</span>
                    </div>
                    <div
                        onClick={() =>
                            handleStatusChange(
                                _RequestDelete.statusTypes.rejected
                            )
                        }
                        className={`px-5 py-1 rounded-sm ${
                            status == _RequestDelete.statusTypes.rejected
                                ? "bg-gray-100 text-gray-700"
                                : "hover:bg-gray-100 hover:text-link"
                        }`}
                    >
                        <span className="text-inherit">Rejected</span>
                    </div>
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <label className="flex items-center pl-2 space-x-2">
                                        <span className="">Domain</span>
                                        <button
                                            onClick={() => callSortName()}
                                            disabled={items.length === 0}
                                        >
                                            <MdOutlineSwapVert />
                                        </button>
                                    </label>
                                </th>
                                <th>
                                    <span>Owner</span>
                                </th>
                                <th>
                                    <span>Reason</span>
                                </th>

                                <th>
                                    <span>EPP Status</span>
                                </th>
                                <th>
                                    <span>Domain Status</span>
                                </th>
                                <th>
                                    <span>Date Requested By</span>
                                </th><th>
                                    <span>Note of Support</span>
                                </th>
                                {!deleteDisabled && (
                                    <th>
                                        <span className="text-xl">
                                            <MdOutlineSettings />
                                        </span>
                                    </th>
                                )}
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {hasSpinner ? (
                                <tr>
                                    <td colSpan={7}>
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg">
                                            <LoaderSpinner
                                                ml="ml-96"
                                                h="h-12"
                                                w="w-12"
                                                position="absolute"
                                            />
                                            <br />
                                            <span className="relative top-9 left-72 ml-20">
                                                Loading Data...
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            ) : (
                                <>
                                    {items.length === 0 ? (
                                        <tr>
                                            <td colSpan="100%">
                                                <SearchSearchNoDomainFound />
                                            </td>
                                        </tr>
                                    ) : (
                                        Object.values(items).map((item) => (
                                            <Item
                                                item={item}
                                                key={
                                                    "pendingDelete-" +
                                                    item.dcrID
                                                }
                                                isSelected={selectedItems.includes(
                                                    item.dcrID
                                                )}
                                                onCheckboxChange={
                                                    handleItemCheckboxChange
                                                }
                                                statusType={status}
                                            />
                                        ))
                                    )}
                                </>
                            )}
                        </tbody>
                    </table>
                </div>
                {hasSpinner ? (
                    " "
                ) : (
                    <>
                        <CursorPaginate
                            onFirstPage={onFirstPage}
                            onLastPage={onLastPage}
                            nextPageUrl={nextPageUrl}
                            previousPageUrl={previousPageUrl}
                            itemCount={itemCount}
                            total={total}
                            itemName={itemName}
                        />
                    </>
                )}
            </div>
        </AdminLayout>
    );
}
