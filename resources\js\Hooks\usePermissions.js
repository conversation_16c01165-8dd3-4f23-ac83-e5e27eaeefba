import { usePage } from '@inertiajs/react';

export function usePermissions()
{
    const { auth } = usePage().props;
    const user = auth.user;

    const hasPermission = (permission) =>
    {
        if (!user)
        {
            return false; 
        }

        // if (user.isSuperAdmin)
        // {
        //     return true
        // }

        return user.permissions?.includes(permission);
    };

    return { hasPermission, user };
}