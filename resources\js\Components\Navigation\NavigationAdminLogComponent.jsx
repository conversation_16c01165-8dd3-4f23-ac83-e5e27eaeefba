//* PACKAGES
import React from 'react';

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* HOOKS
import { usePermissions } from '@/Hooks/usePermissions';

//* TYPES
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* ENUMS
//...

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

export default function NavigationAdminLogComponent({ postRouteName })
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        adminLogs: route().current("admin.logs")
    };

    const links =
    [
        {
            routeName: 'admin.logs',
            hasAccess: hasPermission('admin.logs'),
            isActive : routes.adminLogs,
            label    : 'admin logs'
        },
    ];

    //! STATES
    //...

    //! FUNCTIONS

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) =>
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                >
                                    <div
                                        className='flex gap-4'
                                    >
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}