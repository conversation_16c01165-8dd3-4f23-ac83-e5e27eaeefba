<?php

use App\Modules\Client\Controllers\ClientController;
use App\Modules\Client\Controllers\DomainController;
use App\Modules\Client\Controllers\ClientLogsController;
use App\Modules\Client\Controllers\SessionController;
use App\Modules\Setting\Controllers\ExtensionFeeController;
use App\Modules\Client\Controllers\ClientDomainLogController;
use App\Modules\Client\Controllers\SecurityLogController;
use App\Modules\Client\Controllers\InviteUserController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('client')->group(function () {
    Route::get('/', [ClientController::class, 'index'])->name('client');
    Route::get('/{id}/details', [ClientController::class, 'show'])->whereNumber('id')->name('client.details');
    Route::patch('/update-status', [ClientController::class, 'update'])->name('client.update-status');
    Route::delete('/delete', [ClientController::class, 'destroy'])->name('client.delete');

    Route::get('/extension/fee', [ExtensionFeeController::class, 'get_users'])->name('client.extension.fee');
    Route::post('/extension/fee-create', [ExtensionFeeController::class, 'create_custom_fees'])->name('client.extension.fee-create');
    Route::get('/extension/fee-view', [ExtensionFeeController::class, 'get_user_custom'])->name('client.extension.fee-view');
    Route::delete('/extension/delete', [ExtensionFeeController::class, 'destroy'])->name('client.extension.fee-delete');
    Route::patch('/extension/fee-reset', [ExtensionFeeController::class, 'resetFees'])->name('client.setting.extension.fee-reset');

    Route::get('/{id}/domains', [DomainController::class, 'get'])->whereNumber('id')->name('client.domains');

    Route::get('/logs', [ClientLogsController::class, 'index'])->name('client.logs');
    Route::get('/logs/{user_id}/activities', [ClientLogsController::class, 'activities'])
        ->name('client.logs.activities');
    Route::get('/logs/security-logs', [SecurityLogController::class, 'index'])
        ->name('client.logs.security.all');
    Route::get('/logs/security-logs/{user_id}', [SecurityLogController::class, 'index'])
        ->name('client.logs.security');
    Route::get('/logs/domain-logs', [ClientDomainLogController::class, 'index'])
        ->name('client.logs.domain.all');
    Route::get('/logs/{user_id}/domain-logs', [ClientDomainLogController::class, 'index'])
        ->name('client.logs.domain');

    Route::get('/{userId}/sessions', [SessionController::class, 'index'])->name('client.sessions');
    Route::get('/{sessionId}/session-details', [SessionController::class, 'device_session_details'])->name('client.session.details');

    Route::get('/invite', [InviteUserController::class, 'index'])->name('client.invite');
    Route::get('/resend-invite', [InviteUserController::class, 'resendUserInvitation'])->name('client.invite.resend');
    Route::post('/send-invitation', [InviteUserController::class, 'sendUserInvitation'])->name('client.invite.send');

    Route::get('/logs/security-logs/{id}/payload', [SecurityLogController::class, 'getPayload'])
        ->name('client.logs.security.payload');

    Route::get('/logs/domain-logs/{id}/payload', [ClientDomainLogController::class, 'getPayload'])
        ->name('client.logs.domain.view');

    Route::get('/system-credits', [ClientController::class, 'systemCredits'])->name('system.credits'); //TODO: refactor
    Route::get('/system-credits/adjust', [ClientController::class, 'showChangeBalance'])->name('system.credits.adjust');
    Route::post('/system-credits/store', [ClientController::class, 'store'])->name('system.credits.store');
});

