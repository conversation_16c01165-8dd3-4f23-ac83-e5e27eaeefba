import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";

export default function ConfirmDomainDeletionModal({
    isOpen,
    onClose,
    deletionRequest,
}) {
    const [note, setNote] = useState("");
    const [submitting, setSubmitting] = useState(false);

    const [inputValue, setInputValue] = useState("");
    const [isValid, setIsValid] = useState(false);

    const handleInputChange = (e) => {
        const value = e.target.value;
        setInputValue(value);
        setIsValid(
            value.trim().toLowerCase() ===
                deletionRequest.domainName.toLowerCase()
        );
    };

    const handleConfirm = (domainDeletion) => {
        if (!isValid) return;

        router.post(
            route("domain.delete-request.reject"),
            { 
                domainDeletion: deletionRequest,
                support_note: 'Domain deletion request rejected.',
            },
            {
                onSuccess: () => {
                    toast.success("Domain deletion request rejected.");
                },
                onError: (errors) => {
                    toast.error(
                        "Failed to reject the domain request. Please try again."
                    );
                    console.error(errors);
                },
            }
        );
    };

    const handleSubmit = (e) => {
            e.preventDefault();
            setSubmitting(true);
    
            router.post(
                route("domain.support-feedback.store"),
                {
                    deletion_id: deletionRequest.dcrID,
                    support_note: note,
                },
                {
                    onSuccess: () => {
                        toast.success("Support note submitted successfully.");
                        setSubmitting(false);
                        setTimeout(() => onClose(), 1000); // auto-close modal
                        // Update local state so the form hides
                        setDeletionRequest((prev) => ({
                            ...prev,
                            support_note: note,
                        }));
                    },
                    onError: (errors) => {
                        console.log("Validation errors:", errors);
                        toast.error("Something went wrong. Please try again.");
                        setSubmitting(false);
                    },
                }
            );
        };

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={onClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-30" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-xl font-bold leading-6 text-gray-900"
                                >
                                    {deletionRequest.domainName}
                                </Dialog.Title>
                                {/* Confirmation Input */}
                                <div className="mt-4 mb-4">
                                    <label className="block mb-2 text-sm font-medium text-gray-700">
                                        Please re-type{" "}
                                        <strong className="text-red-600">
                                            {deletionRequest.domainName}
                                        </strong>{" "}
                                        to finalize the rejection process.{" "}
                                        <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        value={inputValue}
                                        onChange={handleInputChange}
                                        placeholder="Enter domain name to confirm"
                                        className="w-full border rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-400 focus:outline-none"
                                    />
                                </div>
                                {/* Buttons */}
                                <div className="flex justify-end gap-3">
                                    <button
                                        className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
                                        onClick={onClose}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        className={`px-4 py-2 rounded-lg text-white ${
                                            isValid
                                                ? "bg-green-600 hover:bg-green-700"
                                                : "bg-green-300 cursor-not-allowed"
                                        }`}
                                        onClick={() => handleConfirm(deletionRequest)}
                                        disabled={!isValid}
                                    >
                                        Reject
                                    </button>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
