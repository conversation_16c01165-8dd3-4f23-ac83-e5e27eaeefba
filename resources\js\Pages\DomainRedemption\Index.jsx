import Checkbox from "@/Components/Checkbox";
import Item from "@/Components/DomainRedemption/Item";
import SecondaryButton from "@/Components/SecondaryButton";
import AdminLayout from "@/Layouts/AdminLayout";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useState } from "react";
import { router } from "@inertiajs/react";
import {
    MdOutlineFilterAlt,
    MdOutlineSettings,
    MdOutlineSwapVert,
} from "react-icons/md";
import { MdFilterList } from "react-icons/md";
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import "react-toastify/dist/ReactToastify.css";
import LoaderSpinner from "@/Components/LoaderSpinner";
import SearchSearchNoDomainFound from "@/Components/Domain/Search/SearchNoDomainFound";
import CreatePaymentModal from "@/Components/DomainRedemption/CreatePaymentModal";
import Filter from "@/Components/DomainRedemption/Filter";

const SORT_TYPE = {
    DOMAIN_ASC: "domain:asc",
    DOMAIN_DESC: "domain:desc",
};

export default function Index({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    const params = route().params
    const [selectAll, setSelectAll] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [showCreatePaymentModal, setShowCreatePaymentModal] = useState(false);
    const orderby = params.orderby ?? SORT_TYPE.DOMAIN_ASC;

    const handleOrderToggle = (newOrder) => {
        // Check if order by value is valid
        if (Object.values(SORT_TYPE).includes(newOrder)) {
            let payload = params;
            payload.orderby = newOrder;
            router.get(route("domain-redemption.view"), payload);
        }
    };

    const getPaymentEligibleIds = () => {
        return items
            .filter((item) => {
                // Only allow if no redemption order exists yet
                return !item.redemption_order_id;
            })
            .map((item) => item.delete_id);
    };

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);

        const paymentEligibleIds = getPaymentEligibleIds();
        setSelectedItems(checked ? paymentEligibleIds : []);
    };

    const handleItemCheckboxChange = (itemId, checked) => {
        setSelectedItems((prevSelectedItems) => {
            const updated = checked
                ? [...prevSelectedItems, itemId]
                : prevSelectedItems.filter((id) => id !== itemId);

            const paymentEligibleIds = getPaymentEligibleIds();
            setSelectAll(updated.length === paymentEligibleIds.length);

            return updated;
        });
    };

    const handleBulkCreatePayment = () => {
        if (selectedItems.length > 0) {
            setShowCreatePaymentModal(true);
        }
    };

    const handleBulkDelete = () => {
        router.post(route("domain-redemption.delete"), { ids: selectedItems });
    };

    const [hasSpinner, setSpinner] = useState(false);
    const query = route().params;
    const [limit, setLimit] = useState(parseInt(query.limit) || 20);

    router.on("start", () => setSpinner(true));
    router.on("finish", () => setSpinner(false));

    const handleLimitChange = (e) => {
        const newLimit = parseInt(getEventValue(e));
        setLimit(newLimit);
        router.get(
            route("domain-redemption.view"),
            { ...route().params, limit: newLimit, page: 1 },
            {
                preserveScroll: true,
                preserveState: true,
            }
        );
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div>
                    <h1 className="text-4xl font-bold pl-3">Domain Deleted</h1>
                    <p className="text-gray-600 pl-3">
                        List of domains that have been deleted and are available for redemption.
                    </p>
                </div>
                {/* <div className="flex items-center space-x-4 justify-end">
                    <SecondaryButton
                        onClick={handleBulkCreatePayment}
                        processing={selectedItems.length == 0}
                    >
                        CREATE PAYMENT SELECTED
                    </SecondaryButton>
                    <SecondaryButton
                        onClick={handleBulkDelete}
                        processing={selectedItems.length == 0}
                    >
                        DELETE SELECTED
                    </SecondaryButton>
                </div> */}
                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px", left: "15px" }}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="flex justify-between items-center mt-4 pt-4 pb-4 pl-3">
                    <div className="flex items-center space-x-2">
                        <MdFilterList className="text-xl" />
                        <span>Filter:</span>
                        <Filter />
                    </div>
                </div>

                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr className="border-b">
                                <th>
                                    <label className="flex items-center pl-2 space-x-2">
                                        {/* <Checkbox
                                            name="select_all"
                                            value="select_all"
                                            checked={selectAll}
                                            handleChange={handleSelectAllChange}
                                        /> */}
                                        <span className="">Domain</span>
                                        <button
                                            onClick={() => handleOrderToggle((orderby == SORT_TYPE.DOMAIN_ASC ? SORT_TYPE.DOMAIN_DESC : SORT_TYPE.DOMAIN_ASC))}
                                            disabled={items.length === 0}
                                        >
                                            {orderby === SORT_TYPE.DOMAIN_ASC ? (
                                                <ImSortAlphaAsc />
                                            ) : (
                                                <ImSortAlphaDesc />
                                            )}
                                        </button>
                                    </label>
                                </th>
                                <th>
                                    <span>Name</span>
                                </th>
                                <th>
                                    <span>Email</span>
                                </th>
                                <th>
                                    <span>Payment Status</span>
                                </th>
                                <th>
                                    <span>Date Deleted</span>
                                </th>
                                <th>
                                    <span>Days Redemption</span>
                                </th>
                                <th>
                                    <span>Deleted By</span>
                                </th>
                                <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {hasSpinner ? (
                                <tr>
                                    <td colSpan={6}>
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg">
                                            <LoaderSpinner
                                                ml="ml-96"
                                                h="h-12"
                                                w="w-12"
                                                position="absolute"
                                            />
                                            <br />
                                            <span className="relative top-9 left-72 ml-20">
                                                Loading Data...
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            ) : (
                                <>
                                    {items.length === 0 ? (
                                        <tr>
                                            <td colSpan="100%">
                                                <SearchSearchNoDomainFound />
                                            </td>
                                        </tr>
                                    ) : (
                                        items.map((item) => (
                                            <Item
                                                key={`domainRedemption-${item.delete_id}`}
                                                item={item}
                                                isSelected={selectedItems.includes(item.delete_id)}
                                                // onCheckboxChange={handleItemCheckboxChange}
                                            />
                                        ))
                                    )}
                                </>
                            )}
                        </tbody>
                    </table>
                </div>
                {!hasSpinner && (
                    <CursorPaginate
                        onFirstPage={onFirstPage}
                        onLastPage={onLastPage}
                        nextPageUrl={nextPageUrl}
                        previousPageUrl={previousPageUrl}
                        itemCount={itemCount}
                        total={total}
                        itemName={itemName}
                    />
                )}
            </div>

            <CreatePaymentModal
                isOpen={showCreatePaymentModal}
                onClose={() => setShowCreatePaymentModal(false)}
                selectedItems={selectedItems}
                domains={items}
            />
        </AdminLayout>
    );
}
