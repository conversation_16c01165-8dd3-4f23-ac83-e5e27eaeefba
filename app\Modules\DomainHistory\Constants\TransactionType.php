<?php

namespace App\Modules\DomainHistory\Constants;

final class TransactionType
{
    const DOMAIN_CREATED = 'Domain Created';

    const DOMAIN_RENEWAL = 'Domain Renewal';

    const DOMAIN_UPDATED = 'Domain Updated';

    const DOMAIN_CONTACT_UPDATED = 'Domain Contact Updated';

    const PUSH_REQUEST_APPROVAL = 'Push Request Approval';

    const PUSH_REQUEST_CANCELLED = 'Push Request Cancelled';

    const PUSH_REQUEST_APPROVED = 'Push Request Approved';

    const PUSH_REQUEST_DECLINED = 'Push Request Declined';

    const TRANSFER_REQUEST_CANCELLED = 'Transfer Request Cancelled';
    
    const TRANSFER_INBOUND_REQUEST = 'Transfer Inbound Request';

    const TRANSFER_INBOUND_APPROVED = 'Transfer Inbound Approved';

    const TRANSFER_INBOUND_REJECTED = 'Transfer Inbound Rejected';

    const TRANSFER_OUTBOUND_REQUEST = 'Transfer Outbound Request';

    const TRANSFER_OUTBOUND_APPROVED = 'Transfer Outbound Approved';

    const TRANSFER_OUTBOUND_REJECTED = 'Transfer Outbound Rejected';

    const DOMAIN_UNLOCKED = 'Domain Unlocked';

    const DOMAIN_LOCKED = 'Domain Locked';

    const NAMESERVER_UPDATED = 'Nameserver Updated';

    const TRANSFER_PURCHASE_COMPLETED = 'Transfer Purchase Completed';

    const TRANSFER_PURCHASE_CANCELLED = 'Transfer Purchase Cancelled';

    const TRANSFER_PURCHASE_PENDING = 'Transfer Purchase Pending';

    const DOMAIN_CATEGORY_UPDATED = 'Domain Category Updated';

    const DOMAIN_REDEMPTION_COMPLETED = 'Domain Redemption Completed';

    public static function getConstants()
    {
        $reflection = new \ReflectionClass(__CLASS__);
        return $reflection->getConstants();
    }
}
