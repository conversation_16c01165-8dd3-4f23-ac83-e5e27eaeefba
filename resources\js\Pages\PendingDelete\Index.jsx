import Checkbox from "@/Components/Checkbox";
import Item from "@/Components/PendingDelete/Item";
import SecondaryButton from "@/Components/SecondaryButton";
import AdminLayout from "@/Layouts/AdminLayout";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useState } from "react";
import { router } from "@inertiajs/react";
import {
    MdFilterList,
    MdOutlineSettings,
    MdOutlineSwapVert,
} from "react-icons/md";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import "react-toastify/dist/ReactToastify.css";
import LoaderSpinner from "@/Components/LoaderSpinner";
import SearchSearchNoDomainFound from "@/Components/Domain/Search/SearchNoDomainFound";
import { _PendingDelete } from "@/Constant/_PendingDelete";
import Filter from "@/Components/PendingDelete/Filter";

export default function Index({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    const defaultStatus = () => {
        const { statusType } = route().params;
        return statusType === undefined
            ? _PendingDelete.statusTypes.pending
            : statusType;
    };

    const callSortName = () => {
        const { statusType, orderby } = route().params;

        let str = "";
        if (orderby == "name:asc") {
            str = "name:desc";
        } else {
            str = "name:asc";
        }

        let payload = {};
        payload.statusType = statusType;
        payload.orderby = str;

        router.get(route("domain.pending-delete.view"), payload);
    };

    const [status, setStatus] = useState(defaultStatus());
    const [selectAll, setSelectAll] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const deleteDisabled = status === _PendingDelete.statusTypes.deleted;

    const handleStatusChange = (statusType) => {
        const { orderby } = route().params;
        let payload = {};

        setSpinner(true);

        setStatus(statusType);
        if (statusType === "PENDING") {
            payload = {};
        } else {
            payload = {
                statusType: statusType,
            };
        }

        payload.orderby = orderby;
        router.get(route("domain.pending-delete.view"), payload);
    };

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);

        const delete_ids = items.map((item) => item.delete_id);

        setSelectedItems(checked ? delete_ids : []);
    };

    const handleItemCheckboxChange = (itemId, checked) => {
        setSelectedItems((prevSelectedItems) => {
            return checked
                ? [...prevSelectedItems, itemId]
                : prevSelectedItems.filter((id) => id !== itemId);
        });

        let temp = [...selectedItems];

        if (temp.includes(itemId)) {
            temp = temp.filter((e) => e != itemId);
        } else {
            temp.push(itemId);
        }

        temp.length == items.length ? setSelectAll(true) : setSelectAll(false);
    };

    const handleResponse = (type) => {
        const routeName = {
            [_PendingDelete.actionTypes.delete]: route(
                "domain.pending-delete.summary"
            ),
            [_PendingDelete.actionTypes.restore]: route(
                "domain.pending-delete.restore.summary"
            ),
        }[type];

        router.post(routeName, { ids: selectedItems });
    };

    const [hasSpinner, setSpinner] = useState(false);

    const query = route().params;

    const [limit, setLimit] = useState(parseInt(query.limit) || 20);

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    const handleLimitChange = (e) => {
        const newLimit = parseInt(getEventValue(e));
        setLimit(newLimit);
        router.get(
            route("domain.pending-delete.view"),
            { ...route().params, limit: newLimit, page: 1 },
            {
                preserveScroll: true,
                preserveState: true,
            }
        );
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div>
                    <h1 className="text-4xl font-bold pl-3">Expired Domains</h1>
                    <p className="text-gray-600 pl-3">
                        List of domains that have been expired for over 40 days
                        and are now scheduled for deletion.
                    </p>
                </div>
                <div
                    className={`flex items-center space-x-4 justify-end ${
                        deleteDisabled && "opacity-0"
                    }`}
                >
                    <SecondaryButton
                        onClick={() =>
                            handleResponse(_PendingDelete.actionTypes.delete)
                        }
                        processing={selectedItems.length == 0}
                    >
                        DELETE
                    </SecondaryButton>
                    <SecondaryButton
                        onClick={() =>
                            handleResponse(_PendingDelete.actionTypes.restore)
                        }
                        processing={selectedItems.length == 0}
                    >
                        RESTORE
                    </SecondaryButton>
                </div>
                <div
                    className="flex justify-start"
                    style={{ position: "relative", top: "20px", left: "15px" }}
                >
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                <div
                    id="sample"
                    className="flex justify-between items-center mt-4 pt-4 pb-4 pl-3"
                >
                    <div className="flex items-center space-x-2 ">
                        <MdFilterList className="text-xl" />
                        <span className="ml-2 text-sm text-gray-600">
                            Filter:
                        </span>
                        <Filter status={status} />
                    </div>
                </div>
                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    <div
                        onClick={() =>
                            handleStatusChange(
                                _PendingDelete.statusTypes.pending
                            )
                        }
                        className={`px-5 py-1 rounded-sm ${
                            status == _PendingDelete.statusTypes.pending
                                ? "bg-gray-100 text-gray-700"
                                : "hover:bg-gray-100 hover:text-link"
                        }`}
                    >
                        <span className="text-inherit">Pending</span>
                    </div>
                    <div
                        onClick={() =>
                            handleStatusChange(
                                _PendingDelete.statusTypes.deleted
                            )
                        }
                        className={`px-5 py-1 rounded-sm ${
                            status == _PendingDelete.statusTypes.deleted
                                ? "bg-gray-100 text-gray-700"
                                : "hover:bg-gray-100 hover:text-link"
                        }`}
                    >
                        <span className="text-inherit">Deleted</span>
                    </div>
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <label className="flex items-center pl-2 space-x-2">
                                        {status !==
                                        _PendingDelete.statusTypes.deleted ? (
                                            <Checkbox
                                                name="select_all"
                                                value="select_all"
                                                checked={selectAll}
                                                handleChange={
                                                    handleSelectAllChange
                                                }
                                            />
                                        ) : (
                                            <Checkbox
                                                disabled={true}
                                                className="pointer-events-none opacity-0"
                                            />
                                        )}
                                        <span className="">Domain</span>
                                        <button
                                            onClick={() => callSortName()}
                                            disabled={items.length === 0}
                                        >
                                            <MdOutlineSwapVert />
                                        </button>
                                    </label>
                                </th>
                                <th>
                                    <span>Name</span>
                                </th>
                                <th>
                                    <span>Email</span>
                                </th>
                                {status ===
                                    _PendingDelete.statusTypes.pending && (
                                    <th>
                                        <span>Days Expired</span>
                                    </th>
                                )}
                                {status ===
                                _PendingDelete.statusTypes.deleted ? (
                                    <th>
                                        <span>Date Deleted</span>
                                    </th>
                                ) : (
                                    <th>
                                        <span>Date Scraped</span>
                                    </th>
                                )}
                                {status ===
                                    _PendingDelete.statusTypes.deleted && (
                                    <th>
                                        <span>Deleted By</span>
                                    </th>
                                )}
                                {!deleteDisabled && (
                                    <th>
                                        <span className="text-xl">
                                            <MdOutlineSettings />
                                        </span>
                                    </th>
                                )}
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {hasSpinner ? (
                                <tr>
                                    <td colSpan={7}>
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg">
                                            <LoaderSpinner
                                                ml="ml-96"
                                                h="h-12"
                                                w="w-12"
                                                position="absolute"
                                            />
                                            <br />
                                            <span className="relative top-9 left-72 ml-20">
                                                Loading Data...
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            ) : (
                                <>
                                    {items.length === 0 ? (
                                        <tr>
                                            <td colSpan="100%">
                                                <SearchSearchNoDomainFound />
                                            </td>
                                        </tr>
                                    ) : (
                                        Object.values(items).map((item) => (
                                            <Item
                                                item={item}
                                                key={
                                                    "pendingDelete-" +
                                                    item.delete_id
                                                }
                                                isSelected={selectedItems.includes(
                                                    item.delete_id
                                                )}
                                                onCheckboxChange={
                                                    handleItemCheckboxChange
                                                }
                                                statusType={status}
                                            />
                                        ))
                                    )}
                                </>
                            )}
                        </tbody>
                    </table>
                </div>
                {hasSpinner ? (
                    " "
                ) : (
                    <>
                        <CursorPaginate
                            onFirstPage={onFirstPage}
                            onLastPage={onLastPage}
                            nextPageUrl={nextPageUrl}
                            previousPageUrl={previousPageUrl}
                            itemCount={itemCount}
                            total={total}
                            itemName={itemName}
                        />
                    </>
                )}
            </div>
        </AdminLayout>
    );
}
