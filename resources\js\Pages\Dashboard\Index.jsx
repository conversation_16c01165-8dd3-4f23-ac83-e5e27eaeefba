import AdminLayout from "@/Layouts/AdminLayout";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    FiU<PERSON>s,
    <PERSON><PERSON>ist,
    FiUser,
    <PERSON>FileText,
    Fi<PERSON>lock,
} from "react-icons/fi";
import { Link, router, usePage } from "@inertiajs/react";
import "react-toastify/dist/ReactToastify.css";

import {
    MdOutlinePersonOutline,
    MdExitToApp,
    MdSecurity,
    MdOutlineCheckCircle,
    MdOutlineLibraryAddCheck,
    MdOutlineContactPage,
    MdOutlineSystemUpdateAlt,
    MdOutlineVerifiedUser,
} from "react-icons/md";
import { HiOutlinePencilSquare } from "react-icons/hi2";
import { TbCategoryPlus } from "react-icons/tb";
export default function Index() {
    const {
        domains = { data: [] },
        transactionTypes,
        logs,
    } = usePage().props;
    const typeIcons = {
        SIGN_IN: <MdOutlineCheckCircle />,
        SIGN_OUT: <MdExitToApp />,
        REGISTER: <MdOutlineLibraryAddCheck />,
        PROFILE_UPDATE: <HiOutlinePencilSquare />,
        SECURITY_UPDATE: <MdSecurity />,
        CATEGORY_UPDATE: <TbCategoryPlus />,
        CONTACT_UPDATE: <MdOutlineContactPage />,
        DOMAIN_UPDATE: <MdOutlineSystemUpdateAlt />,
        IDENTITY_VERIFICATION: <MdOutlineVerifiedUser />,
    };
    const getStatusLabel = (type) => {
        const normalizedType = type.replace(/ /g, "_").toUpperCase();
        const label = (logs[0]?.statuses && logs[0].statuses[normalizedType]) || type;
        const icon = typeIcons[normalizedType] || <MdOutlinePersonOutline />;
        return { label, icon };
    };

    return (
        <AdminLayout>
            <div className="p-6 space-y-6 mx-auto container max-w-[1440px] mt-2 flex flex-col px-5 rounded-lg">
                {/* Domain History & Activity Logs */}
                <div className="grid grid-cols-1 lg1366:grid-cols-2 gap-6">
                    {/* Domain History */}
                    <div className="bg-gray-100 p-4 rounded-xl w-full">
                        <div className="flex items-center gap-2 mb-2">
                            <FiFileText className="text-lg" />
                            <h2 className="text-lg font-semibold">
                                Domain History
                            </h2>
                        </div>
                        <div className="space-y-4 max-h-64 overflow-y-auto pr-2">
                            <div className="min-w-full text-sm">
                                {/* Header */}
                                <div className="flex bg-white border-b font-medium text-left">
                                    <div className="py-2 px-2 w-1/4 rounded-tl-lg">
                                        Domain
                                    </div>
                                    <div className="py-2 px-2 w-2/6">
                                        Last Activity
                                    </div>
                                    <div className="py-2 px-2 w-2/6">
                                        Last Update
                                    </div>
                                    <div className="py-2 px-2 w-auto rounded-tr-lg"></div>
                                </div>

                                {/* Rows */}
                                {Array.isArray(domains.domains.data) &&
                                    domains.domains.data.map((log, index) => (
                                        <div
                                            key={log.id || index}
                                            className="flex hover:bg-gray-50 border-b items-center"
                                        >
                                            <div className="py-2 px-2 w-1/4">
                                                {log.name}
                                            </div>
                                            <div className="py-2 px-2 w-2/6">
                                                {transactionTypes[log.type]}
                                            </div>
                                            <div className="py-2 px-2 w-2/12">
                                                {log.created_at
                                                    ? new Date(
                                                          log.created_at
                                                      ).toLocaleDateString(
                                                          "en-US"
                                                      )
                                                    : "Invalid Date"}
                                            </div>
                                            <div className="py-2 px-2 w-auto">
                                                <Link
                                                    href={route(
                                                        "domain.history.show",
                                                        {
                                                            id: log.id || null,
                                                        }
                                                    )}
                                                    className="text-[#0077a3] rounded-md border border-[#0077a3] p-1.5"
                                                >
                                                    View All Activities
                                                </Link>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        </div>

                        <div className="mt-6 flex justify-center">
                            <Link
                                href={route("domain.history")}
                                className="bg-[#0077a3] text-white px-5 py-2 rounded-md font-medium hover:bg-[#005f85] transition"
                            >
                                View Domain History
                            </Link>
                        </div>
                    </div>

                    {/* My Activity Logs */}
                    <div className="bg-gray-100 p-4 rounded-xl w-full">
                        <div className="flex items-center gap-2 mb-4">
                            <FiClock className="text-xl" />
                            <h2 className="text-lg font-semibold">
                                My Activity Logs
                            </h2>
                        </div>
                        <div className="space-y-4 max-h-64 overflow-y-auto pr-2">
                            {logs.map((log) =>
                                (log.logs || []).map((lg) => {
                                    const { label, icon } = getStatusLabel(
                                        lg.type
                                    );
                                    {JSON.stringify(lg.statuses)}
                                    return (
                                        <div key={lg.id}>
                                            <div className="text-sm font-semibold text-gray-800 mb-2 flex items-center">
                                                <div className="bg-white rounded-md border border-gray-300 px-4 py-2 font-bold">
                                                    {lg.formatted_created_at}
                                                </div>
                                                <div className="flex-grow border-t border-gray-300 ml-2"></div>
                                            </div>
                                            <div className="flex items-center justify-between py-2 px-4 text-sm text-gray-700">
                                                <div className="flex items-center gap-2 overflow-x-auto">
                                                    <div>{icon}</div>
                                                    <div className="flex gap-1">
                                                        <span className="font-medium">
                                                            {label}
                                                        </span>
                                                        <span className="text-gray-600">
                                                            - {lg.message} {lg.email}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div>
                                                    {new Date(
                                                        lg.created_at
                                                    ).toLocaleTimeString(
                                                        "en-US",
                                                        {
                                                            hour: "2-digit",
                                                            minute: "2-digit",
                                                            hour12: true,
                                                        }
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })
                            )}
                        </div>

                        <div className="mt-6 flex justify-center">
                            {/* <button className="bg-[#0077a3] text-white px-5 py-2 rounded-md font-medium hover:bg-[#005f85] transition">
                                View My Activity Logs
                            </button> */}
                            <Link
                                href={route("client.logs.security.all")}
                                className="bg-[#0077a3] text-white px-5 py-2 rounded-md font-medium hover:bg-[#005f85] transition"
                            >
                                View My Activity Logs
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Quick Access */}
                <div className="bg-gray-100 p-6 rounded-xl">
                    <h2 className="text-lg font-semibold mb-4">Quick Access</h2>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                        {[
                            {
                                icon: <FiGlobe />,
                                title: "Access",
                                link: "epp.account",
                            },
                            {
                                icon: <FiUsers />,
                                title: "Users",
                                link: "user-management.role",
                            },
                            {
                                icon: <FiList />,
                                title: "Category",
                                link: "user-management.category",
                            },
                            {
                                icon: <FiUser />,
                                title: "Client",
                                link: "client",
                            },
                            {
                                icon: <FiList />,
                                title: "General",
                                link: "setting.general",
                            },
                        ].map((item, i) => (
                            <div
                                key={i}
                                className="p-4 border rounded-lg hover:shadow cursor-pointer group"
                            >
                                <Link href={route(item.link)}>
                                    <div className="text-2xl text-gray-600 mb-2 group-hover:text-blue-600">
                                        {item.icon}
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-sm font-semibold">
                                            {item.title}
                                        </h3>
                                        <span className="text-xl">›</span>
                                    </div>
                                    <p className="text-xs text-gray-500 mt-2">
                                        Lorem ipsum dolor sit amet, consectetur
                                        adipiscing elit, sed do eiusmod tempor.
                                    </p>
                                </Link>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
