//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdMode, MdOutlinePendingActions } from "react-icons/md";

//* COMPONENTS
import InputLabel from "@/Components/InputLabel";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function GeneralItem(
    {
        item
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const valueType = {
        TEXT: {
            type: "text",
        },
        DOLLAR: {
            type: "number",
            prefix: "$",
        },
        NUMBER: {
            type: "number",
        },
        PERCENT: {
            type: "number",
            suffix: "%",
        },
        DAY: {
            type: "number",
            suffix: (item.value > 1) ? " days" : " day",
        },
    };

    //! STATES
    const [show, setShow]               = useState(false);
    const [updateValue, setUpdateValue] = useState(item.value);
    const [itemValue, setItemValue]     = useState(item.value);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const onHandleChange = (event) => {
        setUpdateValue(getEventValue(event));
    };

    const handleUpdate = () => {
        const newValue = Number.isInteger(updateValue)
            ? updateValue.toString()
            : updateValue;
        setShow(false);

        if (newValue.trim() == "") {
            toast.error("Value is Empty");
            setUpdateValue(itemValue);
            return;
        }
        router.patch(route("setting.general-update"), {
            key: item.key,
            value: newValue,
            type: item.type,
        }, {
            onError: (errors) => toast.error(errors.message, { autoClose: 8000 }),
            onSuccess: () => {
                toast.success("Value updated.");
                setItemValue(newValue);
            },
        });
    };

    const checkUppercaseWords = (words, target) => {
        if (words.includes(target)) {
            const index = words.findIndex(obj => obj === target);
            words[index] = words[index].toUpperCase();
        }
    }

    const convertNameFormat = (str) => {
        str = str.toLowerCase();
        let words = str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1));
        checkUppercaseWords(words, "Vat");
        checkUppercaseWords(words, "Icann");
        return words.join(' ');
    }

    return (
        <div className="hover:bg-gray-100 p-2 rounded-sm">
            <InputLabel forInput="" value={convertNameFormat(item.key.replace(/_/g, " "))} />
            <div className="flex justify-between pb-4">
                <span className="font-semibold">
                    {valueType[item.type].prefix != undefined &&
                        valueType[item.type].prefix}
                    {itemValue}
                    {valueType[item.type].suffix != undefined &&
                        valueType[item.type].suffix}
                </span>
                {
                    hasPermission("setting.general-update") 
                        ?
                            !show 
                                ? 
                                    (
                                        <button
                                            className="inline-flex items-center text-gray-500 hover:text-primary"
                                            onClick={() => setShow(!show)}
                                        >
                                            <MdMode /> Edit
                                        </button>
                                    )
                                :
                                    (
                                        <span className="inline-flex items-center text-gray-500 hover:text-primary cursor-pointer">
                                            <MdOutlinePendingActions /> draft
                                        </span>
                                    )
                        : 
                            null
                }
            
            </div>

            <div className={`${!show && "hidden"}`}>
                <TextInput
                    type={valueType[item.type].type}
                    name="name"
                    placeholder="new value"
                    className="w-full mb-3"
                    value={updateValue}
                    handleChange={onHandleChange}
                />
                <div className="inline-flex space-x-4">
                    <PrimaryButton onClick={() => handleUpdate()}>
                        Save
                    </PrimaryButton>
                    <SecondaryButton onClick={() => setShow(false)}>
                        Cancel
                    </SecondaryButton>
                </div>
            </div>
        </div>
    );
}
