<?php

use App\Modules\Setting\Controllers\CommisionSettingController;
use App\Modules\Setting\Controllers\ExtensionFeeController;
use App\Modules\Setting\Controllers\FeeSettingController;
use App\Modules\Setting\Controllers\GeneralSettingController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('setting')->group(function () {

        Route::get('/general', [GeneralSettingController::class, 'index'])->name('setting.general');
        Route::patch('/general-update', [GeneralSettingController::class, 'update'])->name('setting.general-update');
        
        Route::get('/fee', [FeeSettingController::class, 'index'])->name('setting.fee');
        Route::patch('/fee-update', [FeeSettingController::class, 'update'])->name('setting.fee-update');

        Route::get('/extension/fee', [ExtensionFeeController::class, 'get_default'])->name('setting.extension.fee');
        Route::patch('/extension/fee-update', [ExtensionFeeController::class, 'update'])->name('setting.extension.fee-update');

        Route::get('/commission', [CommisionSettingController::class, 'index'])->name('setting.commission');
    }
);
