<?php

namespace App\Modules\PendingDelete\Services;

use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Constant\DomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class DomainRestoreJobService
{
    public static function instance()
    {
        $domainRestoreJobService = new self;

        return $domainRestoreJobService;
    }

    public function restore(array $domain): void
    {
        $eppInfo = EppDomainService::instance()->callEppDomainInfo($domain['domainName'])['data'];
        $isStatusValid = $this->validateDomainStatus($eppInfo['status']);

        if (!$isStatusValid) {
            NotificationService::sendFailedRestoreActionNotif($domain['domainName']);
            throw new Exception('Domain status is not valid');
        }

        EppDomainService::instance()->databaseSyncExpiry($domain['domainName'], $domain['email']);
        $payload = $this->setToDefaultPayload($domain['domainName']);
        EppDomainService::instance()->updateEppDomain($payload);
        $this->localUpdate($domain['registeredDomainId'], $eppInfo['expiry']);
    }

    private function validateDomainStatus(array $currentStatus): bool
    {
        $requiredStatus = EppDomainStatus::POST_AUTO_RENEWAL_GRACE_PERIOD_STATUS;
        $missingRequiredStatuses = array_diff($requiredStatus, $currentStatus);

        return empty($missingRequiredStatuses);
    }

    private function localUpdate(string $registeredDomainId, string $expiry): void
    {
        DB::client()->table('domains')->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->where('registered_domains.id', $registeredDomainId)
            ->update([
                'domains.status' => DomainStatus::ACTIVE,
                'domains.client_status' => json_encode(EppDomainStatus::DEFAULT_STATUS),
                'domains.expiry' => Carbon::parse($expiry)->getTimestampMs(),
                'domains.updated_at' => Carbon::now()
            ]);
    }

    private function setToDefaultPayload(string $domainName): array
    {
        return [
            'name' => $domainName,
            'statusRemove' => [
                EppDomainStatus::CLIENT_RENEW_PROHIBITED,
                EppDomainStatus::CLIENT_UPDATE_PROHIBITED
            ],
            'status' => [
                EppDomainStatus::CLIENT_DELETE_PROHIBITED
            ]
        ];
    }
}
