//* PACKAGES
import React from 'react';

//* ICONS
import { MdOutlinePayments } from "react-icons/md";

//* COMPONENTS
import DropDownContainer from "@/Components/DropDownContainer";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Item({
    item,
    getStatusLabel,
    activeDropdown,
    toggleDropdown,
    handleView,
    loadingPayload,
    dropdownRefs,
    dropdownContainerRef,
    hasPermission
})
{
    //! PACKAGE
    //...

    //! HOOKS
    //...

    //! VARIABLES
    const { label, icon } = getStatusLabel(item.type);

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const getTypeIcon = (type) => {
        const normalizedType = type.replace(/ /g, "_").toUpperCase();

        const typeIconMap = {
            'PAYMENT_CREATED': <MdOutlinePayments />,
        };

        return typeIconMap[normalizedType] || null;
    };

    // Override icon if we have a custom mapping for this type
    const customIcon = getTypeIcon(item.type);
    const finalIcon = customIcon || icon;


    return (
        <div className="flex items-start px-6">
            <div className="w-1/5 min-w-[240px] pr-4 pt-4">
                <div className="flex items-center space-x-2">
                    <span className="text-xl">
                        {finalIcon}
                    </span>
                    <span>
                        {label}
                    </span>
                </div>
            </div>
            <div className="w-[700px] px-4 pt-4">
                <div className="break-words">
                    <div className="flex flex-col">
                        <span>{item.message}</span>
                    </div>
                </div>
            </div>
            <div className="w-[100px] pl-5 text-left pt-4">
                {new Date(item.created_at).toLocaleString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                })}
            </div>
            <div className="pl-4 pt-4">
                <span
                    ref={(el) => {
                        if (el) dropdownRefs.current[item.id] = el;
                        if (activeDropdown === item.id) dropdownContainerRef.current = el;
                    }}
                    className="relative"
                >
                </span>
            </div>
        </div>
    );
}