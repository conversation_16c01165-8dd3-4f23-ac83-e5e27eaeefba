//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router, usePage } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import {
    FiGlobe,
    FiUsers,
    FiList,
    FiUser,
    FiFileText,
    FiClock,
} from "react-icons/fi";

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialDashboardDomainHistory(
    {
        //! PROPS
        //... 
        
        //! STATES 
        //...
        
        //! EVENTS
        //...
    }
)
{
    //! PACKAGE
    //...

    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const {
        domains = { data: [] },
        transactionTypes,
        logs,
    } = usePage().props;    

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <div className="bg-gray-100 p-4 rounded-xl w-full">
            <div className="flex items-center gap-2 mb-2">
                <FiFileText className="text-lg" />
                <h2 className="text-lg font-semibold">
                    Domain History
                </h2>
            </div>
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                <div className="min-w-full text-sm">
                    {/* Header */}
                    <div className="flex bg-white border-b font-medium text-left">
                        <div className="py-2 px-2 w-1/4 rounded-tl-lg">
                            Domain
                        </div>
                        <div className="py-2 px-2 w-2/6">
                            Last Activity
                        </div>
                        <div className="py-2 px-2 w-2/6">
                            Last Update
                        </div>
                        <div className="py-2 px-2 w-auto rounded-tr-lg"></div>
                    </div>

                    {/* Rows */}
                    {Array.isArray(domains.domains.data) &&
                        domains.domains.data.map((log, index) => (
                            <div
                                key={log.id || index}
                                className="flex hover:bg-gray-50 border-b items-center"
                            >
                                <div className="py-2 px-2 w-1/4">
                                    {log.name}
                                </div>
                                <div className="py-2 px-2 w-2/6">
                                    {transactionTypes[log.type]}
                                </div>
                                <div className="py-2 px-2 w-2/12">
                                    {log.created_at
                                        ? new Date(
                                                log.created_at
                                            ).toLocaleDateString(
                                                "en-US"
                                            )
                                        : "Invalid Date"}
                                </div>
                                {
                                    hasPermission('domain.history.show') 
                                        ?
                                            <div className="py-2 px-2 w-auto">
                                                <Link
                                                    href={route(
                                                        "domain.history.show",
                                                        {
                                                            id: log.id || null,
                                                        }
                                                    )}
                                                    className="text-[#0077a3] text-xs-custom rounded-md border border-[#0077a3] p-1.5"
                                                >
                                                    View All Activities
                                                </Link>
                                            </div>
                                        :
                                            null
                                }
                            
                            </div>
                        ))}
                </div>
            </div>

            <div className="mt-6 flex justify-center">
                {
                    hasPermission('domain.history')  
                        ?
                            <Link
                                href={route("domain.history")}
                                className="bg-[#0077a3] text-white px-5 py-2 rounded-md font-medium hover:bg-[#005f85] transition text-xs-custom"
                            >
                                View Domain History
                            </Link>
                        :
                            null
                }
            </div>
        </div>
    );
}
