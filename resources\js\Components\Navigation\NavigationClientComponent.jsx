//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    MdExpand<PERSON>ess,
    MdExpandMore,
    MdSupervisedUserCircle,
} from "react-icons/md";

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationClientComponent(
    {
        postRouteName
    }
)
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;
    
    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        client       : route().current("client") || route().current("client.domains"),
        extensionFees: route().current("client.extension.fee") || currentRoute.includes('client.extension.fee'),
        // logs         : route().current("client.logs") || currentRoute.includes('client.logs') || route().current("client.logs.security.all") || currentRoute.includes('client.logs.security'),
    };

    const links = 
    [
        {
            routeName: 'client',
            hasAccess: hasPermission('client'),
            isActive : routes.client,
            icon     : <MdSupervisedUserCircle className="text-2xl"/>,
            label    : 'client'
        }, 
        {
            routeName: 'client.extension.fee',
            hasAccess: hasPermission('client.extension.fee'),
            isActive : routes.extensionFees,
            icon     : <MdAttachMoney className="text-2xl"/>,
            label    : 'extension fees'
        }, 
    ];

    //! STATES
    const [stateShow, setStateShow] = useState(Object.values(routes).includes(true));

    //! FUNCTIONS
    const isVisible = () =>
    {
        return !stateShow ? " hidden" : "";
    };

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            <button
                onClick={() => setStateShow(!stateShow)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span
                    className=" text-inherit"
                >
                    Client
                </span>
                {stateShow ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => 
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                    className={isVisible()}
                                >   
                                    <div
                                        className='flex gap-4'
                                    >
                                        {item.icon}
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
