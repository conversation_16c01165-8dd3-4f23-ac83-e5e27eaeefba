<?php

namespace App\Modules\Setting\Constants;

final class FeeType
{
    public const REGISTRATION = 'REGISTRATION';

    public const TRANSFER = 'TRANSFER';

    public const RENEW = 'RENEW';

    public const RENEWAL = 'RENEWAL';

    public const PROTECTION = 'PROTECTION';

    public const PENALTY_LATE_RENEWAL = 'PENALTY_LATE_RENEWAL';

    public const EXTENSION_FEES = [
        self::REGISTRATION,
        self::RENEW,
        self::TRANSFER,
    ];

    public static function all()
    {
        return [self::REGISTRATION, self::TRANSFER, self::RENEW, self::PROTECTION, self::PENALTY_LATE_RENEWAL];
    }

    public static function getText($type)
    {
        if ($type == self::RENEW) {
            return 'RENEWAL';
        } else {
            return $type;
        }
    }
}
