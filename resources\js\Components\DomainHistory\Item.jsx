import React from 'react';
import { Link } from "@inertiajs/react";
import Checkbox from "@/Components/Checkbox";
import Status from "./DomainStatusIndicators";

function Item({ domain, transactionTypes, isSelected, onCheckboxChange }) {
    const handleCheckboxChange = (e) => {
        const isChecked = e.target.checked;
        onCheckboxChange(domain.id, domain.is_active, isChecked);
    };

    // console.log(domain);

    return (
        <tr className="border-t mt-2">
            <td className="py-2 px-4 pt-4 pb-4">
                <label className="flex items-center space-x-2">
                    <div style={{ visibility: domain.status === 'ACTIVE' ? 'visible' : 'hidden' }}>
                        <Checkbox
                            name="domain"
                            value={domain.id}
                            checked={isSelected}
                            handleChange={handleCheckboxChange}
                        />
                    </div>
                    <span>{domain.name}</span>
                </label>
            </td>
            <td className="py-2 px-4">
                {transactionTypes[domain.type] || 'Unknown Type'}
            </td>
            <td className="py-2 px-4">
                {domain.domain_email}
            </td>
            <td className="py-2 px-4">
                {domain.created_at
                    ? new Date(Date.parse(domain.created_at)).toLocaleDateString()
                    : "Invalid Date"}
            </td>
            <td className="py-2 px-4">
                <Status
                    domain={domain}
                    locked_until={domain.locked_until}
                />
            </td>
            <td className="py-2 px-4">
                <Link
                    href={route("domain.history.show", { id: domain.id || null })}
                    className="block px-0 text-center py-2 bg-primary border border-transparent rounded-md font-semibold text-sm 
                            text-white tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 
                            focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition
                            ease-in-out duration-150"
                >
                    View All Logs
                </Link>
            </td>
        </tr>
    );
}

export default Item;
