import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useRef, useState } from "react";
import { MdMoreVert } from "react-icons/md";
import "react-toastify/dist/ReactToastify.css";
import getRecentTime from "@/Util/getRecentTime";
import getRemainingTime from "@/Util/getRemainingTime";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";
import { router } from "@inertiajs/react";
import { _PendingDelete } from "@/Constant/_PendingDelete";

export default function Item({ item, isSelected, onCheckboxChange, statusType }) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.delete_id, getEventValue(e));
    };

    const handleResponse = (type) => {
        const routeName = {
            [_PendingDelete.actionTypes.delete]: route("domain.pending-delete.summary"),
            [_PendingDelete.actionTypes.restore]: route("domain.pending-delete.restore.summary"),
        }[type];

        router.post(routeName, { ids: [item.delete_id] });
    };

    const onChangeDateTimeFormat = (format, time) => {
        switch (format) {
            case 1:
                return <span>{setDefaultDateFormat(time)}</span>
            case 2:
                if (Date.now() > time) return <span className="text-red-500">{getRecentTime(time)}</span>
                return <span className="text-green-500 font-bold">{getRemainingTime(time)}</span>
            default:
                return <span>{setDefaultDateFormat(time)}</span>
        };
    }

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    <Checkbox
                        name="select"
                        value="select"
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                        disabled={statusType === _PendingDelete.statusTypes.deleted}
                        className={`${statusType === _PendingDelete.statusTypes.deleted ? "opacity-0 pointer-events-none" : ""}`}
                    />
                    <span className="cursor-pointer">
                        {item.name}
                    </span>
                </label>
            </td>
            <td><span>{item.first_name} {item.last_name}</span></td>
            <td><span>{item.user_email}</span></td>
            {statusType === _PendingDelete.statusTypes.pending && (
                <td><span>{onChangeDateTimeFormat(2, item.expiry)}</span></td>
            )}
            {statusType === _PendingDelete.statusTypes.deleted ? (
                <>
                    <td><span>{item.domain_deleted_at}</span></td>
                    <td><span>{item.deleted_by}</span></td>
                </>
            ) : (<td>{item.scraped_at}</td>)}
            <td>
                {statusType === _PendingDelete.statusTypes.pending && (
                    <span ref={ref} className="relative">
                        <button
                            className="flex items-center"
                            onClick={() => setShow(!show)}
                        >
                            <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                        </button>
                        <DropDownContainer show={show}>
                            <button
                                className="hover:bg-gray-100 px-5 py-1 justify-start flex"
                                onClick={() => handleResponse(_PendingDelete.actionTypes.delete)}
                            >
                                Delete
                            </button>
                            <button
                                className="hover:bg-gray-100 px-5 py-1 justify-start flex"
                                onClick={() => handleResponse(_PendingDelete.actionTypes.restore)}
                            >
                                Restore
                            </button>
                        </DropDownContainer>
                    </span>
                )}
            </td>

        </tr>
    );
}
