<?php

namespace App\Modules\RequestDelete\Services;

use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\Epp\Services\EppDomainService;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        $DomainDeleteService = new self;

        return $DomainDeleteService;
    }

    public function approveDeleteRequestSave($request)
    {
        $eppInfo = EppDomainService::instance()->callEppDomainInfo($request->domainDeletion['domain_id']);
        $datastoreInfo = EppDomainService::instance()->callDatastoreDomainInfo($request->domainDeletion['domain_id']);
        $data = $request->all();
        $date = Carbon::parse($data['domainDeletion']['created_at']);
        // dd(isset($datastoreInfo['data'])?$datastoreInfo['data'] : $datastoreInfo);
        // Check if the date is within the last 5 days from now && isset($eppInfo['data'])
        if ($date->greaterThanOrEqualTo(now()->subDays(5))) {

            EppDomainService::instance()->callEppDomainDelete($data['domainDeletion']['domain_id']);
            EppDomainService::instance()->callDatastoreDomainDelete($data['domainDeletion']['domain_id']);

            self::localDelete($data);

            //send notification to user's dashboard
            self::userNotification($data);

            //send email to user
            self::userEmailNotification($data);

            return;
        } else {

            EppDomainService::instance()->callEppDomainDelete($data['domainDeletion']['domain_id']);
            EppDomainService::instance()->callDatastoreDomainDelete($data['domainDeletion']['domain_id']);

            self::localDelete($data);

            //send notification to user
            self::userNotification($data);

            //send email to user
            self::userEmailNotification($data);

            //Pending delete
            DB::client()->table('pending_domain_deletions')->insert([
                'registered_domain_id' => $data['domainDeletion']['domain_id'],
                'deleted_by' => Auth::user()->email,
                'deleted_at' => now(),
            ]);
        }
    }
    public function rejectDeleteRequestSave($request)
    {

        self::updateDomainDeletionRequestTable($request, 0);

        //update domain table
        DB::client()->table('domains')->where('id', $request['domainDeletion']['domain_id'])->update([
            'status' => DomainStatus::ACTIVE,
            'deleted_at' => null,
            'updated_at' => now(),
        ]);
    }
    private function localDelete($requestData)
    {
        // Update domain deletion request entry
        self::updateDomainDeletionRequestTable($requestData);

        $domainId = $requestData['domainDeletion']['domain_id'];
        $timestamp = now();
        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        // Update registered_domains table
        DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->update($updates);

        // Update domains table
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update($updates);
    }

    private function updateDomainDeletionRequestTable($requestData, $authID = null)
    {
        $agentID = $authID ?? Auth::id();

        $date = Carbon::parse($requestData['domainDeletion']['created_at']);

        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        return DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainDeletion']['domain_id'])
            ->update([
                'support_agent_id' => $agentID,
                'support_agent_name' =>  Auth::user()->name . ' (' . Auth::user()->email . ')',
                'deleted_at' => now(),
                'feedback_date' => now(),
                'support_note' => $requestData['support_note'],
                'is_refunded' => $is_refunded,
            ]);
    }

    private function userNotification($requestData)
    {

        DB::client()->table('notifications')->insert([
            'user_id'      => $requestData['domainDeletion']['user_id'],
            'title'        => 'Domain Deletion Request Approved',
            'message'      => 'Your request to delete the domain "' . $requestData['domainDeletion']['domainName'] . '" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.',
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotification($requestData)
    {
        $message = [
            'subject'  => 'Domain Deletion Request Approved',
            'greeting' => 'Greetings!',
            'body'     => 'Your request to delete the domain "' . $requestData['domainDeletion']['domainName'] . '" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.',
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($requestData['domainDeletion']['email'])->send(new UserDeleteRequestMail($message));
    }

}
