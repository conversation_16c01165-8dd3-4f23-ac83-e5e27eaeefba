import React, { useState, useRef } from "react";
import AdminLayout from "@/Layouts/AdminLayout";
import { usePage } from "@inertiajs/react";
import { IoMdArrowBack } from "react-icons/io";
import { MdFilterList, MdMoreVert } from "react-icons/md";
import Filter from "@/Components/DomainHistory/ViewLog/Filter";
import { router } from "@inertiajs/react";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { toast } from "react-toastify";

export default function ViewIndex() {
    const {
        domainLogs = { data: [] },
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
        logMessages,
        ipDetails,
        transactionTypes,
        id,
    } = usePage().props;

    const [activeDropdown, setActiveDropdown] = useState(null);
    const dropdownRefs = useRef([]);
    const [lowerCaseParams, setLowerCaseParams] = useState({});
    const isOnFirstPage = !previousPageUrl;
    const isOnLastPage = !nextPageUrl;

    useOutsideClick(dropdownRefs, () => {
        setActiveDropdown(null);
    });

    const toggleDropdown = (index) => {
        setActiveDropdown(activeDropdown === index ? null : index);
    };

    const groupLogsByDate = (logs) => {
        if (!Array.isArray(logs)) return {};

        return logs.reduce((groups, log) => {
            const date = log.formatted_update;
            if (!groups[date]) {
                groups[date] = [];
            }
            groups[date].push(log);
            return groups;
        }, {});
    };

    const logsArray = Array.isArray(domainLogs)
        ? domainLogs
        : domainLogs.data || [];
    const groupedLogs = groupLogsByDate(logsArray);

    const [filteredDomain, setFilteredDomain] = useState(
        logsArray.length > 0 ? logsArray[0].domain : "No Domain Info"
    );

    const onBackClick = () => {
        router.visit(route("domain.history"), {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getTransactionTypeLabel = (type) =>
        transactionTypes[type] || "Unknown Type";

    const handleSearchChange = (searchParams) => {
        const lowerCaseParams = Object.fromEntries(
            Object.entries(searchParams).map(([key, value]) => [
                key,
                value ? value.toLowerCase() : "",
            ])
        );

        setLowerCaseParams(lowerCaseParams);

        router.get(
            route("domain.history.show", { id: id, ...lowerCaseParams }),
            { preserveState: true }
        );
    };

    const handlePageChange = (url) => {
        router.get(
            url,
            { id: id, ...lowerCaseParams },
            {
                preserveState: true,
                onSuccess: (page) => {
                    const { domainLogs, total, itemCount } = page.props;
                    setLowerCaseParams({
                        ...lowerCaseParams,
                        domainLogs,
                        total,
                        itemCount,
                    });
                },
            }
        );
    };

    const handleClientHold = (domainId, domainName) => {
        router.put(
            route("domains.client-hold.update"),
            { 
                domain_id: domainId,
                domain_name: domainName 
            },
            {
                onSuccess: () => {
                    toast.success(`Client Hold Status Updating...`);
                    setActiveDropdown(null);
                },
                onError: (errors) => {
                    toast.error(errors.message || "Failed to update domain status");
                },
            }
        );
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-full mt-20">
                <div className="flex space-x-4 px-5">
                    <button onClick={onBackClick} className="text-2xl pt-1">
                        <IoMdArrowBack />
                    </button>
                    <h1 className="text-3xl">Domain Logs: {filteredDomain}</h1>
                </div>
                <div className="flex justify-between items-center mt-4 pt-4 pb-4 pl-9">
                    <div className="flex items-center space-x-2 mt-4">
                        <MdFilterList className="text-xl" />
                        <span>Filter:</span>
                        <Filter
                            onFilter={handleSearchChange}
                            transactionTypes={transactionTypes}
                        />
                    </div>
                </div>
                <div className="bg-white p-6">
                    {Object.keys(groupedLogs).map((date) => (
                        <div key={date} className="mb-6">
                            <div className="flex items-center">
                                <div className="bg-white rounded-md border border-gray-300 px-4 py-2 font-bold">
                                    {date}
                                </div>
                                <div className="flex-grow border-t border-gray-300 ml-2"></div>
                            </div>
                            <table className="min-w-full bg-white">
                                <tbody>
                                    {groupedLogs[date].map((log, index) => (
                                        <tr key={index}>
                                            <td className="w-1/5 py-2">
                                                <div className="flex items-center">
                                                    {getTransactionTypeLabel(
                                                        log.type
                                                    )}
                                                </div>
                                            </td>
                                            <td className="w-1/4 py-2">
                                                {logMessages[index] ||
                                                    "No log message available"}
                                            </td>
                                            <td className="w-1/5 py-2 text-center">
                                                {log.created_at
                                                    ? new Date(
                                                          log.created_at + "Z"
                                                      ).toLocaleString(
                                                          "en-US",
                                                          {
                                                              hour: "2-digit",
                                                              minute: "2-digit",
                                                              timeZone: "UTC",
                                                          }
                                                      )
                                                    : "Invalid Date"}
                                            </td>
                                            <td className="w-1/5 py-2">
                                                {log.domain_email}
                                            </td>
                                            {/* <td className="w-1/5 py-2 text-center relative">
                                                <div 
                                                    ref={el => dropdownRefs.current[index] = el}
                                                    className="inline-block"
                                                >
                                                    <button
                                                        type="button"
                                                        className="flex items-center"
                                                        onClick={() => toggleDropdown(index)}
                                                    >
                                                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                                                    </button>
                                                    <DropDownContainer show={activeDropdown === index}>
                                                        <button
                                                            type="button"
                                                            className="px-5 py-1 hover:bg-gray-100 flex justify-start"
                                                            onClick={() => handleClientHold(log.domain_id, log.domain)}
                                                        >
                                                            Add Client Hold
                                                        </button>
                                                    </DropDownContainer>
                                                </div>
                                            </td> */}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ))}
                </div>
                <div className="m-6">
                    <CursorPaginate
                        onFirstPage={isOnFirstPage}
                        onLastPage={isOnLastPage}
                        nextPageUrl={nextPageUrl}
                        previousPageUrl={previousPageUrl}
                        itemCount={itemCount}
                        total={total}
                        itemName="domain"
                        onPageChange={handlePageChange}
                    />
                </div>
            </div>
        </AdminLayout>
    );
}
