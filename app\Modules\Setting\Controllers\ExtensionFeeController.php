<?php

namespace App\Modules\Setting\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Requests\ShowListRequest;
use App\Modules\Setting\Constants\ExtensionFeeType;
use App\Modules\Setting\Requests\ExtensionFeeDeleteRequest;
use App\Modules\Setting\Requests\ExtensionFeeUpdateRequest;
use App\Modules\Setting\Services\ExtensionFeeService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ExtensionFeeController extends Controller
{
    public function get_default()
    {
        return Inertia::render('Setting/ExtensionFee/Default', [
            'fees' => ExtensionFeeService::instance()->getDefault(),
            'extension_type' => ExtensionFeeType::SETTINGS,
        ]);
    }

    public function update(ExtensionFeeUpdateRequest $request)
    {
        $validator = $request->update();

        if ($validator && $validator->errors()) {
            return redirect()->back()->withErrors($validator->errors()->messages());
        }

        return redirect()->back();
    }

    public function get_users(ShowListRequest $request)
    {
        $data = $request->extension_fees_show();

        return Inertia::render('Client/ExtensionFee/Index', $data);
    }

    public function get_user_custom(Request $request)
    {
        $userId = $request->id;
        $data['fees'] = ExtensionFeeService::instance()->getCustomFeeByUserId($userId);
        $data['name'] = $request->name;
        $data['userId'] = $userId;
        $data['extension_type'] = ExtensionFeeType::CLIENT;

        return Inertia::render('Client/ExtensionFee/View', $data);
    }

    public function create_custom_fees(Request $request)
    {

        $userId = $request->id;
        ExtensionFeeService::instance()->store($userId);

        return redirect()->back();
    }

    public function destroy(ExtensionFeeDeleteRequest $request)
    {
        $request->softDelete();

        return redirect()->route('client.extension.fee');
    }

    public function resetFees(Request $request)
    {
        ExtensionFeeService::instance()->resetFees($request->id);

        return redirect()->back();
    }
}
